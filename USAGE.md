# OneNET IoT Hub - 使用指南

## 🚀 功能概览

### 1. 主页 - 数据概览
- **访问地址**: `/`
- **功能**: 显示系统总体数据统计和最新接收的数据
- **特性**: 
  - 实时数据统计
  - 最新数据记录预览
  - 数据库初始化检查

### 2. 数据查看 - 详细数据管理
- **访问地址**: `/data`  
- **功能**: 查看所有接收到的OneNET推送数据
- **特性**:
  - 按设备筛选数据
  - 原始数据详情查看
  - 数据统计信息

### 3. 数据分析 - 多设备对比 ⭐
- **访问地址**: `/analytics`
- **功能**: 多设备数据对比和趋势分析
- **核心特性**:
  - ✅ **多设备选择**: 同时选择多个设备进行对比
  - ✅ **时间范围选择**: 自定义分析时间段
  - ✅ **实时监控图表**: 显示最新10分钟的数据趋势
  - ✅ **历史趋势对比**: 显示选定时间范围的数据对比
  - ✅ **统计分析**: 平均值、最大值、最小值对比
  - ✅ **数据导出**: 支持CSV格式导出

### 4. 测试工具 - 接口测试
- **访问地址**: `/test-webhook`
- **功能**: 测试OneNET推送服务功能
- **特性**:
  - URL验证测试
  - OneNET标准推送格式测试
  - 兼容性测试

## 📊 数据分析功能详解

### 设备选择
1. 进入分析页面后，系统会自动加载所有可用设备
2. 每个设备显示:
   - 设备名称（如果有）
   - 设备ID
   - 最新数据值和时间

### 时间范围设置
- **默认**: 最近7天
- **自定义**: 点击日期范围选择器设置开始和结束日期
- 支持的格式: 任意日期范围

### 数据流类型选择
- 系统自动识别所有可用的数据流类型
- 常见类型: temperature（温度）、humidity（湿度）、pressure（压力）等

### 图表类型

#### 1. 实时监控图表
- **更新频率**: 每5秒自动刷新
- **数据范围**: 最近10分钟
- **最大数据点**: 30个
- **控制**: 可暂停/恢复自动刷新

#### 2. 历史趋势对比图
- **显示**: 选定时间范围内的完整数据趋势
- **交互**: 鼠标悬停查看具体数值
- **多设备**: 不同颜色线条区分各设备

#### 3. 统计分析图
- **平均值**: 选定时间范围内的平均数据
- **最大值**: 最高数据点
- **最小值**: 最低数据点

### 数据导出
- **格式**: CSV文件
- **内容**: 包含所有选定设备在选定时间范围内的数据
- **文件名**: 自动生成包含数据流类型和时间戳

## 🔧 OneNET配置

### 推送地址设置
在OneNET平台配置以下信息:
- **推送URL**: `https://your-domain.com/api/onenet/webhook`
- **Token**: 在环境变量 `ONENET_TOKEN` 中设置
- **加密密钥**: （可选）在环境变量 `ONENET_AES_KEY` 中设置

### 环境变量
```env
# OneNET推送服务配置
ONENET_TOKEN=your_actual_token_here
ONENET_AES_KEY=your_16_char_key  # 可选，仅加密模式需要

# 数据库配置
DATABASE_URL=your_database_url_here
```

## 📈 使用示例

### 场景1: 监控多个传感器温度对比
1. 进入 `/analytics` 页面
2. 选择数据流类型: `temperature`
3. 勾选要对比的设备（如: sensor-room1, sensor-room2, sensor-room3）
4. 设置时间范围（如: 最近24小时）
5. 点击"开始分析"
6. 查看实时图表和历史趋势对比

### 场景2: 导出一周的湿度数据
1. 选择数据流类型: `humidity`
2. 选择相关设备
3. 设置时间范围: 最近7天
4. 点击"开始分析"
5. 点击"导出数据"下载CSV文件

### 场景3: 实时监控设备状态
1. 选择要监控的设备和数据流
2. 确保"自动刷新"开关已开启
3. 实时图表将每5秒更新一次最新数据
4. 可以暂停刷新查看历史趋势

## 🎨 界面特性

### 颜色编码
- **蓝色**: 主要数据和平均值
- **绿色**: 最大值和正常状态
- **黄色**: 最小值和警告
- **红色**: 异常值和错误状态

### 响应式设计
- 支持桌面、平板和手机访问
- 图表自动适应屏幕大小
- 触摸友好的交互设计

## 🔍 故障排除

### 常见问题

1. **没有数据显示**
   - 检查OneNET推送配置是否正确
   - 确认数据库已正确初始化
   - 查看服务器日志

2. **实时图表不更新**
   - 检查是否开启了自动刷新
   - 确认最近10分钟内有新数据
   - 检查网络连接

3. **图表数据不准确**
   - 确认时间范围设置正确
   - 检查设备ID和数据流ID是否匹配
   - 查看原始数据确认

### 性能优化
- 建议同时选择的设备数量不超过10个
- 长时间范围分析时建议分批进行
- 定期清理旧数据以保持性能

## 📱 移动端使用
- 所有功能在手机浏览器中都可正常使用
- 图表支持触摸缩放和滑动
- 表格支持横向滚动查看完整数据

---

## 🆘 技术支持

如遇问题，请检查:
1. 服务器运行日志
2. 浏览器控制台错误
3. OneNET平台推送状态
4. 数据库连接状态
